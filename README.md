# 🔥 响应式个人简历网页
使用HTML+CSS+JavaScript构建的响应式网站，使用原生的JavaScript开发，利用动画插件库实现过滤和滑动以及滚动效果，提升交互体验，实现页面根据设备大小自适应，可一键切换暗黑/明亮模式，符合当前页面开发的流行趋势。

图标: [https://boxicons.com/](https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbEl6c0VlNU5KNEpDYmpwX2JpTXFNQ2V2cVdGZ3xBQ3Jtc0trTTZNYlptQWNud0d5UFpENnJaRXBGN3RPX0xZa0hsVExKQ1d6R1FpWGFKaVlDVEFYand5SkVaczJGOFlXTUdMV2txZmpXNFBDZlY5ZWlIQkp2MG94NVZBalVMTi1IczduNFc4SUVLc1dSNjlGLVpNYw&q=https%3A%2F%2Fboxicons.com%2F) 

字体: https://www.iconfont.cn/

动画过滤和排序: [https://www.kunkalabs.com/mixitup/](https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbWRYdDZJNXF3SkRLN1E4VFBpYTdUQ1lMczk2Z3xBQ3Jtc0trNGl3b1h1SEJMSmZGS3p2WmhqeV9QMDFVX1p3TUVHWkVRSVptSDBmUXFaTFd1Ukw4VGh5eUlfWkdTUEVkRmVVTWtOWmNBSUpTLUMtdVotOXVCS0ktNzJqbm1oLWpLcHdRWjd6UUVFb1YzWU1EUnY3QQ&q=https%3A%2F%2Fwww.kunkalabs.com%2Fmixitup%2F) 

滑动效果: [https://swiperjs.com/](https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbTlTZ2N5bnQ1b3ZESUtIemtzazlOd2hPODZLZ3xBQ3Jtc0ttMml4d3d6S0IyRTZPVVJnc216cVpUZXpzaVdOUG56WTJTbjEyeW9qUURJY0ZiWlRlampEU2FhN0tDNWlUVThpOEZNd2xQR1B3MlREaGZfS2U3Qm45dTNoX3lpWmVJV3FxaWhwZVExb2dobWdlWHgxMA&q=https%3A%2F%2Fswiperjs.com%2F) 

滚动效果: [https://scrollrevealjs.org/](https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbXhXcWt0R3llYmd0RnotNVBCemxHVU5WRVkwUXxBQ3Jtc0treFdIa184c2VuNVhKMm5XdTFfTkhUcUUzYmJXNC1pb3JYdUN3cXdraHJRdWVtUTBDX1VBbkdmdlkyb2RfMDVjaEdKODQ3WDJnMlF0LVVJUXkwVUZkcDZZc1hKZHEwVjZBVFVkRDFHU3hGZWU2eHNRYw&q=https%3A%2F%2Fscrollrevealjs.org%2F) 

配色: [https://colors.dopely.top/color-pedia](https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbkpuUnczQ2w3R1JoMGd1ZUFNN1REaGJkTnVEQXxBQ3Jtc0tuYnVlejdhVjJVNVBVMDBSWjFyZnZReXVYd00ySzhJalhQUWRwRFUzN1l6ay1EcDFacktram5YTmxaUFBPOUxnQ1RUVWRMaFNoZU81WFNUTE5ON3ZPSmtFR2RxSXNqdjRDRGhJMjZ0cGtXSVNWSkY2OA&q=https%3A%2F%2Fcolors.dopely.top%2Fcolor-pedia)

# 项目预览

## Home

<img width="189" alt="preview1" src="https://user-images.githubusercontent.com/71574640/152921643-23836b92-428f-474b-9b81-5575f4f073ee.png">


## Section1

<img width="189" alt="preview2" src="https://user-images.githubusercontent.com/71574640/152921654-c599dc29-401c-469e-815b-d645756ce24d.png">


## Section2

<img width="189" alt="preview3" src="https://user-images.githubusercontent.com/71574640/152921665-af4b1309-21a8-4971-81e7-c92df5b5cf83.png">


## Section3

<img width="189" alt="preview4" src="https://user-images.githubusercontent.com/71574640/152921668-aae9fb8f-4a96-4748-a84e-18a0ef2de049.png">


## Section4

<img width="189" alt="preview5" src="https://user-images.githubusercontent.com/71574640/152921672-3fcbae89-57e5-46ba-a3f8-4b5199f9911d.png">


## Footer

<img width="189" alt="preview6" src="https://user-images.githubusercontent.com/71574640/152921687-077bbf4c-4668-4d5b-b6fc-893af596d99d.png">

